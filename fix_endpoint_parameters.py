#!/usr/bin/env python3
"""
修复端点参数定义的脚本
为所有POST端点的业务参数添加Body()包装
"""

import os
import re
from pathlib import Path

def fix_endpoint_parameters():
    """修复端点参数定义"""
    
    endpoints_dir = Path("zentao-mcp-backend-service/app/api/v1/endpoints/zentao")
    
    # 需要修复的文件列表
    files_to_fix = [
        "projects.py",
        "tasks.py", 
        "stories.py",
        "bugs.py",
        "users.py",
        "analysis.py"
    ]
    
    for file_name in files_to_fix:
        file_path = endpoints_dir / file_name
        if not file_path.exists():
            print(f"⚠️  文件不存在: {file_path}")
            continue
            
        print(f"🔧 修复文件: {file_name}")
        
        # 读取文件内容
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否已经导入Body
        if 'from fastapi import' in content and 'Body' not in content:
            # 添加Body导入
            content = re.sub(
                r'from fastapi import ([^)]+)',
                r'from fastapi import \1, Body',
                content
            )
            print(f"  ✅ 添加Body导入")
        
        # 查找需要修复的参数
        # 匹配模式：函数参数中的业务参数（不是Depends的参数）
        def fix_parameters(match):
            func_def = match.group(0)
            
            # 跳过已经有Body的参数
            if 'Body(' in func_def:
                return func_def
            
            # 修复业务参数
            lines = func_def.split('\n')
            fixed_lines = []
            
            for line in lines:
                # 跳过装饰器和函数定义行
                if line.strip().startswith('@') or 'async def' in line:
                    fixed_lines.append(line)
                    continue
                
                # 跳过Depends参数
                if 'Depends(' in line:
                    fixed_lines.append(line)
                    continue
                
                # 修复业务参数
                if ':' in line and '=' not in line and line.strip().endswith(','):
                    # 这是一个业务参数，需要添加Body()
                    param_match = re.match(r'(\s*)(\w+):\s*([^,]+),', line)
                    if param_match:
                        indent, param_name, param_type = param_match.groups()
                        fixed_line = f"{indent}{param_name}: {param_type} = Body(...),"
                        fixed_lines.append(fixed_line)
                        continue
                
                fixed_lines.append(line)
            
            return '\n'.join(fixed_lines)
        
        # 应用修复
        pattern = r'@router\.post[^)]+\)[^a]*?async def [^(]+\([^)]+\):'
        content = re.sub(pattern, fix_parameters, content, flags=re.MULTILINE | re.DOTALL)
        
        # 写回文件
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"  ✅ 修复完成")
    
    print(f"\n🎉 所有文件修复完成")

if __name__ == "__main__":
    try:
        fix_endpoint_parameters()
    except Exception as e:
        print(f"❌ 修复过程中出错: {e}")
        import traceback
        traceback.print_exc()
