#!/usr/bin/env python3
"""
修复客户端工具返回类型的脚本
将所有 List[Dict[str, Any]] 改为 Dict[str, Any]
"""

import re

def fix_return_types():
    """修复返回类型"""
    file_path = "zentao-mcp-client/zentao_mcp_client/proxy.py"
    
    # 读取文件
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 查找所有需要修复的行
    pattern = r'async def (\w+)\([^)]*\) -> List\[Dict\[str, Any\]\]:'
    matches = re.findall(pattern, content)
    
    print(f"发现 {len(matches)} 个需要修复的函数:")
    for match in matches:
        print(f"  - {match}")
    
    # 替换所有匹配项
    new_content = re.sub(
        r'-> List\[Dict\[str, Any\]\]:',
        r'-> Dict[str, Any]:',
        content
    )
    
    # 写回文件
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(new_content)
    
    print(f"\n✅ 已修复所有返回类型")

if __name__ == "__main__":
    fix_return_types()
