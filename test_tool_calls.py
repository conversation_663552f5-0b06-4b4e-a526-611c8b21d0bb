#!/usr/bin/env python3
"""
测试工具调用的脚本
验证修复后的返回类型是否正确
"""

import asyncio
import sys
from pathlib import Path

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent / "zentao-mcp-client"))

from zentao_mcp_client.config import ClientConfig
from zentao_mcp_client.proxy import ZentaoMCPProxy

async def test_tool_calls():
    """测试工具调用"""
    print("🧪 测试工具调用")
    print("=" * 50)
    
    # 初始化配置
    config = ClientConfig()
    
    if not config.is_configured():
        print("❌ 客户端未配置，请先配置backend_url和api_key")
        return False
    
    proxy = ZentaoMCPProxy(config)
    
    try:
        # 测试基本工具调用
        test_cases = [
            {
                "name": "zentao_get_all_departments",
                "args": {},
                "description": "获取所有部门"
            },
            {
                "name": "zentao_get_all_projects", 
                "args": {},
                "description": "获取所有项目"
            },
            {
                "name": "zentao_get_users_by_dept",
                "args": {"dept_id": 1},
                "description": "根据部门获取用户"
            },
            {
                "name": "zentao_get_tasks_by_project",
                "args": {"project_id": 1},
                "description": "根据项目获取任务"
            }
        ]
        
        success_count = 0
        total_count = len(test_cases)
        
        for test_case in test_cases:
            try:
                print(f"🔍 测试 {test_case['name']} - {test_case['description']}")
                
                result = await proxy._forward_request(test_case['name'], test_case['args'])
                
                if result:
                    # 检查返回结果的结构
                    if isinstance(result, dict):
                        print(f"✅ 成功 - 返回类型: dict")
                        if 'rsCode' in result:
                            print(f"   响应码: {result.get('rsCode')}")
                        if 'body' in result:
                            body = result.get('body')
                            if isinstance(body, list):
                                print(f"   数据条数: {len(body)}")
                            else:
                                print(f"   数据类型: {type(body).__name__}")
                        success_count += 1
                    else:
                        print(f"⚠️  返回类型异常: {type(result).__name__}")
                else:
                    print(f"❌ 返回空结果")
                    
            except Exception as e:
                print(f"❌ 调用失败: {e}")
            
            print()
        
        # 统计结果
        print("📊 测试结果:")
        print(f"总测试数: {total_count}")
        print(f"成功数: {success_count}")
        print(f"失败数: {total_count - success_count}")
        print(f"成功率: {success_count/total_count*100:.1f}%")
        
        if success_count == total_count:
            print("\n🎉 所有工具调用测试通过！")
            return True
        else:
            print("\n⚠️  部分工具调用测试失败")
            return False
            
    finally:
        await proxy.close()

async def test_return_type_validation():
    """测试返回类型验证"""
    print("\n🔍 测试返回类型验证")
    print("=" * 50)
    
    config = ClientConfig()
    proxy = ZentaoMCPProxy(config)
    
    try:
        # 测试一个简单的调用
        result = await proxy._forward_request("zentao_get_all_departments", {})
        
        print(f"返回结果类型: {type(result).__name__}")
        
        if isinstance(result, dict):
            print("✅ 返回类型正确 (Dict)")
            
            # 检查标准响应结构
            expected_keys = ['rsCode', 'rsMsg', 'body']
            missing_keys = [key for key in expected_keys if key not in result]
            
            if not missing_keys:
                print("✅ 响应结构完整")
            else:
                print(f"⚠️  缺少字段: {missing_keys}")
            
            return True
        else:
            print(f"❌ 返回类型错误，期望 Dict，实际 {type(result).__name__}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False
    finally:
        await proxy.close()

async def main():
    """主函数"""
    try:
        print("🚀 开始工具调用测试")
        print("=" * 60)
        
        # 测试返回类型验证
        type_test_ok = await test_return_type_validation()
        
        # 测试工具调用
        call_test_ok = await test_tool_calls()
        
        # 总结
        print("\n" + "=" * 60)
        print("📋 测试总结")
        print("=" * 60)
        
        if type_test_ok:
            print("✅ 返回类型验证通过")
        else:
            print("❌ 返回类型验证失败")
        
        if call_test_ok:
            print("✅ 工具调用测试通过")
        else:
            print("❌ 工具调用测试失败")
        
        overall_success = type_test_ok and call_test_ok
        
        if overall_success:
            print("\n🎉 所有测试通过！返回类型修复成功。")
        else:
            print("\n⚠️  部分测试失败，请检查问题。")
        
        return overall_success
        
    except Exception as e:
        print(f"\n💥 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⚠️ 测试被用户中断")
        sys.exit(1)
