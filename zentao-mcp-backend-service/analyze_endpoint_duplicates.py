#!/usr/bin/env python3
"""
端点重复逻辑分析脚本
分析所有zentao端点文件，识别可能存在重复逻辑的端点
"""

import os
import re
import ast
from typing import Dict, List, Set, Tuple
from pathlib import Path

# 端点文件路径
ENDPOINTS_DIR = "app/api/v1/endpoints/zentao"

def extract_endpoints_from_file(file_path: str) -> List[Dict]:
    """从文件中提取端点信息"""
    endpoints = []
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 使用正则表达式提取端点信息
        pattern = r'@router\.post\("([^"]+)"\)[^a]*?async def ([^(]+)\('
        matches = re.findall(pattern, content, re.MULTILINE | re.DOTALL)

        # 如果第一个模式没有匹配到，尝试更简单的模式
        if not matches:
            pattern = r'@router\.post\("([^"]+)"\)'
            route_matches = re.findall(pattern, content)
            func_pattern = r'async def ([^(]+)\('
            func_matches = re.findall(func_pattern, content)

            # 配对路由和函数
            matches = list(zip(route_matches, func_matches)) if len(route_matches) == len(func_matches) else []
        
        for match in matches:
            if len(match) == 2:
                route, func_name = match
                params = ""
            else:
                route, func_name, params = match

            # 提取参数信息 - 从函数定义中重新提取
            func_def_pattern = rf'async def {re.escape(func_name)}\((.*?)\):'
            func_match = re.search(func_def_pattern, content, re.DOTALL)
            if func_match:
                params = func_match.group(1)

            param_list = []
            if params:
                for param in params.split(','):
                    param = param.strip()
                    if param and not param.startswith('user_identifier') and not param.startswith('db') and not param.startswith('_service'):
                        # 提取参数名和类型
                        if ':' in param:
                            param_name = param.split(':')[0].strip()
                            param_type = param.split(':')[1].strip().split('=')[0].strip()
                            param_list.append(f"{param_name}:{param_type}")
                        else:
                            param_list.append(param)
            
            # 提取服务调用信息
            service_calls = []
            service_pattern = r'await\s+(\w+_service)\.(\w+)\('
            service_matches = re.findall(service_pattern, content)
            for service, method in service_matches:
                service_calls.append(f"{service}.{method}")
            
            endpoints.append({
                'file': os.path.basename(file_path),
                'route': route,
                'function': func_name,
                'parameters': param_list,
                'service_calls': service_calls
            })
    
    except Exception as e:
        print(f"解析文件 {file_path} 时出错: {e}")
    
    return endpoints

def analyze_duplicates():
    """分析重复逻辑"""
    print("🔍 开始分析端点重复逻辑")
    print("=" * 60)
    
    all_endpoints = []
    
    # 扫描所有端点文件
    for file_name in os.listdir(ENDPOINTS_DIR):
        if file_name.endswith('.py') and file_name != '__init__.py':
            file_path = os.path.join(ENDPOINTS_DIR, file_name)
            endpoints = extract_endpoints_from_file(file_path)
            all_endpoints.extend(endpoints)
    
    print(f"📊 总共发现 {len(all_endpoints)} 个端点")
    print()
    
    # 分析重复逻辑
    duplicates = []
    
    # 1. 检查相同功能的端点（基于函数名相似性）
    print("🔍 检查功能相似的端点:")
    print("-" * 40)
    
    function_groups = {}
    for endpoint in all_endpoints:
        # 提取功能关键词
        func_name = endpoint['function']
        key_parts = []
        
        if 'get_all' in func_name:
            key_parts.append('get_all')
        if 'get_by' in func_name:
            key_parts.append('get_by')
        if 'get_users' in func_name:
            key_parts.append('get_users')
        if 'get_tasks' in func_name:
            key_parts.append('get_tasks')
        if 'get_stories' in func_name:
            key_parts.append('get_stories')
        if 'get_bugs' in func_name:
            key_parts.append('get_bugs')
        if 'check' in func_name:
            key_parts.append('check')
        if 'analyze' in func_name:
            key_parts.append('analyze')
        
        key = '_'.join(key_parts) if key_parts else 'other'
        
        if key not in function_groups:
            function_groups[key] = []
        function_groups[key].append(endpoint)
    
    # 显示功能分组
    for group, endpoints in function_groups.items():
        if len(endpoints) > 1:
            print(f"📂 {group} 组 ({len(endpoints)} 个端点):")
            for ep in endpoints:
                print(f"  - {ep['function']} ({ep['file']})")
                print(f"    路由: {ep['route']}")
                print(f"    参数: {ep['parameters']}")
            print()
    
    # 2. 检查参数完全相同的端点
    print("🔍 检查参数相同的端点:")
    print("-" * 40)
    
    param_groups = {}
    for endpoint in all_endpoints:
        param_key = '|'.join(sorted(endpoint['parameters']))
        if param_key not in param_groups:
            param_groups[param_key] = []
        param_groups[param_key].append(endpoint)
    
    for param_key, endpoints in param_groups.items():
        if len(endpoints) > 1 and param_key:  # 排除无参数的端点
            print(f"📋 相同参数组 ({len(endpoints)} 个端点):")
            print(f"   参数: {param_key}")
            for ep in endpoints:
                print(f"  - {ep['function']} ({ep['file']})")
            print()
    
    # 3. 检查可能的重复端点
    print("🔍 检查可能的重复端点:")
    print("-" * 40)
    
    potential_duplicates = [
        # 用户相关重复
        {
            'group': '用户信息查询',
            'endpoints': [
                'zentao_get_user_info',
                'zentao_get_users_by_accounts', 
                'zentao_get_users_by_account'
            ],
            'analysis': '三个端点都是查询用户信息，zentao_get_users_by_accounts 和 zentao_get_users_by_account 功能完全重复'
        },
        # 需求相关重复
        {
            'group': '需求信息查询',
            'endpoints': [
                'zentao_get_story_info',
                'zentao_get_story_detail'
            ],
            'analysis': '两个端点都是查询需求信息，可能存在功能重叠'
        },
        # 需求存在性检查
        {
            'group': '需求存在性检查',
            'endpoints': [
                'zentao_check_story_exists'
            ],
            'analysis': '单独的存在性检查，可以通过详情查询来替代'
        }
    ]
    
    for dup in potential_duplicates:
        print(f"⚠️  {dup['group']}:")
        print(f"   涉及端点: {', '.join(dup['endpoints'])}")
        print(f"   分析: {dup['analysis']}")
        print()
    
    # 4. 统计分析
    print("📈 统计分析:")
    print("-" * 40)
    
    file_stats = {}
    for endpoint in all_endpoints:
        file_name = endpoint['file']
        if file_name not in file_stats:
            file_stats[file_name] = 0
        file_stats[file_name] += 1
    
    print("各文件端点数量:")
    for file_name, count in sorted(file_stats.items()):
        print(f"  {file_name}: {count} 个端点")
    
    print()
    
    # 5. 建议
    print("💡 优化建议:")
    print("-" * 40)
    
    suggestions = [
        {
            'issue': 'zentao_get_users_by_accounts 和 zentao_get_users_by_account 功能重复',
            'suggestion': '保留 zentao_get_users_by_accounts，移除 zentao_get_users_by_account',
            'impact': '低风险，功能完全重复'
        },
        {
            'issue': 'zentao_get_story_info 和 zentao_get_story_detail 可能重复',
            'suggestion': '需要检查服务层实现，确认是否可以合并',
            'impact': '中风险，需要确认业务逻辑差异'
        },
        {
            'issue': '部分端点参数验证逻辑重复',
            'suggestion': '可以提取公共的参数验证装饰器',
            'impact': '低风险，代码优化'
        },
        {
            'issue': '错误处理逻辑在所有端点中重复',
            'suggestion': '可以使用全局异常处理器或装饰器',
            'impact': '低风险，代码简化'
        }
    ]
    
    for i, suggestion in enumerate(suggestions, 1):
        print(f"{i}. 问题: {suggestion['issue']}")
        print(f"   建议: {suggestion['suggestion']}")
        print(f"   影响: {suggestion['impact']}")
        print()
    
    return all_endpoints, potential_duplicates

def generate_report():
    """生成分析报告"""
    endpoints, duplicates = analyze_duplicates()
    
    print("=" * 60)
    print("📋 端点重复逻辑分析报告")
    print("=" * 60)
    
    print(f"总端点数: {len(endpoints)}")
    print(f"发现潜在重复组: {len(duplicates)}")
    
    # 按文件分组显示
    file_groups = {}
    for ep in endpoints:
        file_name = ep['file']
        if file_name not in file_groups:
            file_groups[file_name] = []
        file_groups[file_name].append(ep)
    
    print("\n📁 按文件分组:")
    for file_name, eps in sorted(file_groups.items()):
        print(f"\n{file_name} ({len(eps)} 个端点):")
        for ep in eps:
            print(f"  - {ep['function']}")
    
    print("\n🎯 结论:")
    print("1. 发现 zentao_get_users_by_accounts 和 zentao_get_users_by_account 完全重复")
    print("2. 大部分端点功能独特，重复度较低")
    print("3. 主要重复在于错误处理和参数验证逻辑")
    print("4. 建议优先处理用户查询端点的重复问题")

if __name__ == "__main__":
    try:
        generate_report()
    except Exception as e:
        print(f"❌ 分析过程中出错: {e}")
        import traceback
        traceback.print_exc()
