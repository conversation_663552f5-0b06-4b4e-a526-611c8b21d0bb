#!/usr/bin/env python3
"""
端点重复逻辑分析报告
基于实际扫描的端点信息进行分析
"""

def generate_duplicate_analysis_report():
    """生成重复逻辑分析报告"""
    
    # 实际扫描到的端点信息
    endpoints = {
        # 分析相关端点 (7个)
        'analysis.py': [
            'zentao_get_project_analysis',
            'analyze_bugs_by_dept_and_time', 
            'analyze_story_workload',
            'project_summary_analysis',
            'personnel_workload_analysis',
            'story_task_relation_query',
            'bug_to_story_tracking'
        ],
        # Bug相关端点 (4个)
        'bugs.py': [
            'zentao_get_bugs_by_time_range',
            'zentao_get_bug_detail',
            'zentao_get_personal_bugs',
            'zentao_get_bugs_by_time_and_dept'
        ],
        # 部门相关端点 (2个)
        'departments.py': [
            'zentao_get_all_departments',
            'zentao_get_users_by_dept'
        ],
        # 项目相关端点 (4个)
        'projects.py': [
            'zentao_get_all_projects',
            'zentao_get_tasks_by_project',
            'zentao_get_stories_by_project',
            'zentao_get_bugs_by_project'
        ],
        # 需求相关端点 (5个)
        'stories.py': [
            'zentao_get_story_info',
            'zentao_get_story_detail',
            'zentao_get_stories_end_info',
            'zentao_check_story_exists',
            'zentao_get_stories_by_time'
        ],
        # 任务相关端点 (3个)
        'tasks.py': [
            'zentao_get_task_detail',
            'zentao_get_tasks_by_account',
            'zentao_get_tasks_by_dept'
        ],
        # 用户相关端点 (3个)
        'users.py': [
            'zentao_get_user_info',
            'zentao_get_users_by_accounts',
            'zentao_get_users_by_account'
        ]
    }
    
    print("🔍 端点重复逻辑分析报告")
    print("=" * 80)
    
    total_endpoints = sum(len(eps) for eps in endpoints.values())
    print(f"📊 总计: {total_endpoints} 个端点，分布在 {len(endpoints)} 个文件中")
    
    # 显示各文件端点分布
    print("\n📁 文件端点分布:")
    print("-" * 50)
    for file_name, eps in endpoints.items():
        print(f"{file_name:15} {len(eps):2d} 个端点")
        for ep in eps:
            print(f"  - {ep}")
        print()
    
    # 重复逻辑分析
    print("🔍 重复逻辑分析:")
    print("=" * 80)
    
    duplicates_found = []
    
    # 1. 用户查询端点重复分析
    user_endpoints = endpoints['users.py']
    print("👥 用户相关端点分析:")
    print("-" * 40)
    for ep in user_endpoints:
        print(f"  - {ep}")
    
    # 检查用户端点重复
    if 'zentao_get_users_by_accounts' in user_endpoints and 'zentao_get_users_by_account' in user_endpoints:
        duplicates_found.append({
            'severity': '高',
            'type': '完全重复',
            'endpoints': ['zentao_get_users_by_accounts', 'zentao_get_users_by_account'],
            'file': 'users.py',
            'reason': '两个端点功能完全相同，都是根据账号列表批量查询用户信息',
            'evidence': '参数类型相同(List[str])，调用相同的服务方法',
            'suggestion': '保留 zentao_get_users_by_accounts，移除 zentao_get_users_by_account',
            'impact': '低风险 - 功能完全重复，移除不影响业务'
        })
        print("  ⚠️  发现完全重复: zentao_get_users_by_accounts 和 zentao_get_users_by_account")
    
    # 2. 需求查询端点重复分析
    story_endpoints = endpoints['stories.py']
    print("\n📋 需求相关端点分析:")
    print("-" * 40)
    for ep in story_endpoints:
        print(f"  - {ep}")
    
    # 检查需求端点重复
    if 'zentao_get_story_info' in story_endpoints and 'zentao_get_story_detail' in story_endpoints:
        duplicates_found.append({
            'severity': '中',
            'type': '可能重复',
            'endpoints': ['zentao_get_story_info', 'zentao_get_story_detail'],
            'file': 'stories.py',
            'reason': '两个端点都是查询需求信息，功能可能重叠',
            'evidence': '都接受需求ID参数，返回需求详细信息',
            'suggestion': '需要检查服务层实现，确认业务逻辑差异后决定是否合并',
            'impact': '中风险 - 需要确认业务逻辑差异'
        })
        print("  ⚠️  可能重复: zentao_get_story_info 和 zentao_get_story_detail")
    
    # 3. 时间范围查询模式分析
    time_range_endpoints = []
    for file_name, eps in endpoints.items():
        for ep in eps:
            if 'by_time' in ep or 'time_range' in ep:
                time_range_endpoints.append((ep, file_name))
    
    if time_range_endpoints:
        print("\n⏰ 时间范围查询端点:")
        print("-" * 40)
        for ep, file_name in time_range_endpoints:
            print(f"  - {ep} ({file_name})")
        
        if len(time_range_endpoints) >= 3:
            duplicates_found.append({
                'severity': '低',
                'type': '模式重复',
                'endpoints': [ep for ep, _ in time_range_endpoints],
                'file': '多个文件',
                'reason': '多个端点使用相同的时间范围查询模式',
                'evidence': '都接受start_date和end_date参数',
                'suggestion': '可以提取公共的时间范围验证装饰器',
                'impact': '低风险 - 代码优化，提高可维护性'
            })
            print("  💡 建议: 可以提取公共的时间范围查询装饰器")
    
    # 4. 按部门查询模式分析
    dept_query_endpoints = []
    for file_name, eps in endpoints.items():
        for ep in eps:
            if 'by_dept' in ep or 'dept' in ep:
                dept_query_endpoints.append((ep, file_name))
    
    if dept_query_endpoints:
        print("\n🏢 部门查询端点:")
        print("-" * 40)
        for ep, file_name in dept_query_endpoints:
            print(f"  - {ep} ({file_name})")
    
    # 5. 按项目查询模式分析
    project_query_endpoints = []
    for file_name, eps in endpoints.items():
        for ep in eps:
            if 'by_project' in ep:
                project_query_endpoints.append((ep, file_name))
    
    if project_query_endpoints:
        print("\n🏗️  项目查询端点:")
        print("-" * 40)
        for ep, file_name in project_query_endpoints:
            print(f"  - {ep} ({file_name})")
    
    # 重复问题总结
    print("\n🎯 重复问题总结:")
    print("=" * 80)
    
    if duplicates_found:
        for i, dup in enumerate(duplicates_found, 1):
            print(f"\n{i}. 【{dup['severity']}】{dup['type']}")
            print(f"   文件: {dup['file']}")
            print(f"   端点: {', '.join(dup['endpoints'])}")
            print(f"   原因: {dup['reason']}")
            print(f"   证据: {dup['evidence']}")
            print(f"   建议: {dup['suggestion']}")
            print(f"   影响: {dup['impact']}")
    else:
        print("✅ 未发现明显的重复逻辑")
    
    # 代码质量分析
    print("\n📋 代码质量分析:")
    print("=" * 80)
    
    quality_observations = [
        "✅ 端点命名规范统一，都使用 zentao_ 前缀",
        "✅ 文件组织清晰，按业务模块分离",
        "✅ 所有端点都使用 POST 方法，风格一致",
        "⚠️  错误处理逻辑在所有文件中重复",
        "⚠️  参数验证逻辑在所有文件中重复",
        "💡 可以考虑提取公共的异常处理装饰器",
        "💡 可以考虑提取公共的参数验证装饰器"
    ]
    
    for obs in quality_observations:
        print(f"  {obs}")
    
    # 优化建议
    print("\n💡 优化建议:")
    print("=" * 80)
    
    suggestions = [
        {
            'priority': '高',
            'action': '移除重复的用户查询端点',
            'detail': '删除 zentao_get_users_by_account，保留 zentao_get_users_by_accounts',
            'benefit': '减少代码冗余，简化API接口'
        },
        {
            'priority': '中',
            'action': '确认需求查询端点的业务差异',
            'detail': '检查 zentao_get_story_info 和 zentao_get_story_detail 的服务层实现',
            'benefit': '明确业务边界，避免功能重叠'
        },
        {
            'priority': '低',
            'action': '提取公共装饰器',
            'detail': '为时间范围验证、错误处理、参数验证创建公共装饰器',
            'benefit': '提高代码复用性，减少维护成本'
        }
    ]
    
    for i, sug in enumerate(suggestions, 1):
        print(f"{i}. 【{sug['priority']}优先级】{sug['action']}")
        print(f"   详情: {sug['detail']}")
        print(f"   收益: {sug['benefit']}")
        print()
    
    # 最终统计
    print("📈 最终统计:")
    print("=" * 80)
    print(f"总端点数: {total_endpoints}")
    print(f"重复问题: {len(duplicates_found)}")
    print(f"高优先级问题: {len([d for d in duplicates_found if d['severity'] == '高'])}")
    print(f"中优先级问题: {len([d for d in duplicates_found if d['severity'] == '中'])}")
    print(f"低优先级问题: {len([d for d in duplicates_found if d['severity'] == '低'])}")
    
    return duplicates_found

if __name__ == "__main__":
    try:
        duplicates = generate_duplicate_analysis_report()
        print(f"\n🏁 分析完成，发现 {len(duplicates)} 个重复问题")
    except Exception as e:
        print(f"❌ 分析过程中出错: {e}")
        import traceback
        traceback.print_exc()
