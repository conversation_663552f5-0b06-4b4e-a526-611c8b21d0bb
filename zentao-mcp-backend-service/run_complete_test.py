#!/usr/bin/env python3
"""
完整的接口测试脚本
"""

import asyncio
import httpx
import json
import time
from typing import Dict, Any, List

# 测试配置
BASE_URL = "http://127.0.0.1:8000"
API_KEY = "test-api-key-12345"

# 完整的测试用例
TEST_CASES = [
    # 基础健康检查
    {
        "name": "health_check",
        "endpoint": "/health",
        "method": "GET",
        "data": None,
        "category": "system"
    },
    
    # 直接接口测试
    {
        "name": "zentao_get_all_projects",
        "endpoint": "/api/v1/zentao/projects/zentao_get_all_projects",
        "method": "POST",
        "data": {},
        "category": "direct"
    },
    {
        "name": "zentao_get_all_departments",
        "endpoint": "/api/v1/zentao/departments/zentao_get_all_departments",
        "method": "POST",
        "data": {},
        "category": "direct"
    },
    {
        "name": "zentao_get_users_by_account",
        "endpoint": "/api/v1/zentao/users/zentao_get_users_by_account",
        "method": "POST",
        "data": {"accounts": ["admin", "test"]},
        "category": "direct"
    },
    
    # 数据加工接口测试
    {
        "name": "analyze_story_workload",
        "endpoint": "/api/v1/zentao/analysis/analyze_story_workload",
        "method": "POST",
        "data": {"project_id": 1, "include_completed": True},
        "category": "analysis"
    },
    {
        "name": "project_summary_analysis",
        "endpoint": "/api/v1/zentao/analysis/project_summary_analysis",
        "method": "POST",
        "data": {"project_id": 1},
        "category": "analysis"
    },

    # 系统监控工具测试 - 这些端点已被移除，暂时注释掉
    # {
    #     "name": "mcp_get_health_status",
    #     "endpoint": "/api/v1/mcp/tools/mcp_get_health_status",
    #     "method": "POST",
    #     "data": {},
    #     "category": "system"
    # },
    # {
    #     "name": "mcp_get_performance_metrics",
    #     "endpoint": "/api/v1/mcp/tools/mcp_get_performance_metrics",
    #     "method": "POST",
    #     "data": {},
    #     "category": "system"
    # }
]


async def test_interface(client: httpx.AsyncClient, test_case: Dict[str, Any]) -> Dict[str, Any]:
    """测试单个接口"""
    start_time = time.time()
    
    try:
        headers = {"Content-Type": "application/json"}
        
        # 只有MCP工具接口需要API Key
        if "/mcp/tools/" in test_case["endpoint"]:
            headers["X-API-Key"] = API_KEY
        
        if test_case["method"] == "POST":
            response = await client.post(
                test_case["endpoint"],
                json=test_case["data"],
                headers=headers,
                timeout=30.0
            )
        else:
            response = await client.get(
                test_case["endpoint"],
                headers=headers,
                timeout=30.0
            )
        
        duration = time.time() - start_time
        
        result = {
            "name": test_case["name"],
            "category": test_case["category"],
            "status_code": response.status_code,
            "success": response.status_code == 200,
            "duration": round(duration, 3),
            "response_size": len(response.content)
        }
        
        if response.status_code == 200:
            try:
                response_data = response.json()
                result["response_preview"] = str(response_data)[:200] + "..." if len(str(response_data)) > 200 else str(response_data)
            except:
                result["response_preview"] = response.text[:200] + "..." if len(response.text) > 200 else response.text
        else:
            result["error"] = response.text[:200]
        
        return result
        
    except Exception as e:
        duration = time.time() - start_time
        return {
            "name": test_case["name"],
            "category": test_case["category"],
            "status_code": 0,
            "success": False,
            "duration": round(duration, 3),
            "error": str(e)
        }


async def run_tests():
    """运行所有测试"""
    print("🚀 开始运行完整接口测试...")
    print(f"测试目标: {BASE_URL}")
    print(f"测试用例数量: {len(TEST_CASES)}")
    print("-" * 60)
    
    async with httpx.AsyncClient(base_url=BASE_URL) as client:
        results = []
        
        for i, test_case in enumerate(TEST_CASES, 1):
            print(f"[{i:2d}/{len(TEST_CASES)}] 测试 {test_case['name']}...", end=" ")
            
            result = await test_interface(client, test_case)
            results.append(result)
            
            if result["success"]:
                print(f"✅ ({result['duration']}s)")
            else:
                print(f"❌ {result.get('status_code', 'ERROR')}")
                if result.get('error'):
                    print(f"      错误: {result['error'][:100]}")
        
        return results


def generate_report(results: List[Dict[str, Any]]):
    """生成测试报告"""
    print("\n" + "=" * 60)
    print("📊 测试结果统计")
    print("=" * 60)
    
    # 按类别统计
    categories = {}
    for result in results:
        category = result["category"]
        if category not in categories:
            categories[category] = {"total": 0, "success": 0, "failed": 0}
        
        categories[category]["total"] += 1
        if result["success"]:
            categories[category]["success"] += 1
        else:
            categories[category]["failed"] += 1
    
    # 打印类别统计
    for category, stats in categories.items():
        success_rate = (stats["success"] / stats["total"] * 100) if stats["total"] > 0 else 0
        print(f"{category.upper():12} | 总数: {stats['total']:2d} | 成功: {stats['success']:2d} | 失败: {stats['failed']:2d} | 成功率: {success_rate:5.1f}%")
    
    # 总体统计
    total_tests = len(results)
    successful_tests = len([r for r in results if r["success"]])
    failed_tests = total_tests - successful_tests
    overall_success_rate = (successful_tests / total_tests * 100) if total_tests > 0 else 0
    
    print("-" * 60)
    print(f"总体统计     | 总数: {total_tests:2d} | 成功: {successful_tests:2d} | 失败: {failed_tests:2d} | 成功率: {overall_success_rate:5.1f}%")
    
    # 性能统计
    durations = [r["duration"] for r in results if r["success"]]
    if durations:
        avg_duration = sum(durations) / len(durations)
        max_duration = max(durations)
        min_duration = min(durations)
        print(f"性能统计     | 平均: {avg_duration:.3f}s | 最快: {min_duration:.3f}s | 最慢: {max_duration:.3f}s")
    
    # 失败详情
    failed_results = [r for r in results if not r["success"]]
    if failed_results:
        print("\n❌ 失败接口详情:")
        for result in failed_results:
            print(f"  - {result['name']}: {result.get('error', result.get('status_code', 'Unknown error'))}")
    
    return {
        "total_tests": total_tests,
        "successful_tests": successful_tests,
        "failed_tests": failed_tests,
        "success_rate": overall_success_rate,
        "categories": categories,
        "failed_results": failed_results
    }


async def main():
    """主函数"""
    try:
        # 运行测试
        results = await run_tests()
        
        # 生成报告
        report = generate_report(results)
        
        # 保存详细结果
        output_data = {
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            "summary": report,
            "detailed_results": results
        }
        
        with open("complete_test_results.json", "w", encoding="utf-8") as f:
            json.dump(output_data, f, ensure_ascii=False, indent=2)
        
        print(f"\n📄 详细结果已保存到: complete_test_results.json")
        
        # 返回成功状态
        return report["success_rate"] >= 80.0
        
    except Exception as e:
        print(f"\n💥 测试执行失败: {e}")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)