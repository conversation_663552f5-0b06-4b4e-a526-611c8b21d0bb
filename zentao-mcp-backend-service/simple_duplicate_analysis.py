#!/usr/bin/env python3
"""
简单的端点重复逻辑分析
直接检查文件内容，识别重复的端点
"""

import os
import re
from pathlib import Path

def analyze_endpoints():
    """分析端点重复逻辑"""
    print("🔍 端点重复逻辑分析报告")
    print("=" * 60)
    
    endpoints_dir = "app/api/v1/endpoints/zentao"
    all_endpoints = {}
    
    # 扫描所有端点文件
    for file_name in os.listdir(endpoints_dir):
        if file_name.endswith('.py') and file_name != '__init__.py':
            file_path = os.path.join(endpoints_dir, file_name)
            
            print(f"\n📁 分析文件: {file_name}")
            print("-" * 40)
            
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 提取端点信息
            routes = re.findall(r'@router\.post\("([^"]+)"\)', content)
            functions = re.findall(r'async def ([^(]+)\(', content)
            
            print(f"发现 {len(routes)} 个端点:")
            for i, (route, func) in enumerate(zip(routes, functions)):
                print(f"  {i+1}. {func}")
                print(f"     路由: {route}")
                all_endpoints[func] = {
                    'file': file_name,
                    'route': route,
                    'function': func
                }
    
    print(f"\n📊 总计: {len(all_endpoints)} 个端点")
    
    # 分析重复逻辑
    print("\n🔍 重复逻辑分析:")
    print("=" * 60)
    
    # 1. 用户相关端点分析
    user_endpoints = [ep for ep in all_endpoints.keys() if 'user' in ep.lower()]
    if user_endpoints:
        print("\n👥 用户相关端点:")
        for ep in user_endpoints:
            print(f"  - {ep} ({all_endpoints[ep]['file']})")
        
        # 检查重复
        if 'zentao_get_users_by_accounts' in user_endpoints and 'zentao_get_users_by_account' in user_endpoints:
            print("  ⚠️  发现重复: zentao_get_users_by_accounts 和 zentao_get_users_by_account 功能相同")
    
    # 2. 需求相关端点分析
    story_endpoints = [ep for ep in all_endpoints.keys() if 'story' in ep.lower() or 'stories' in ep.lower()]
    if story_endpoints:
        print("\n📋 需求相关端点:")
        for ep in story_endpoints:
            print(f"  - {ep} ({all_endpoints[ep]['file']})")
        
        # 检查重复
        if 'zentao_get_story_info' in story_endpoints and 'zentao_get_story_detail' in story_endpoints:
            print("  ⚠️  可能重复: zentao_get_story_info 和 zentao_get_story_detail 功能可能重叠")
    
    # 3. 任务相关端点分析
    task_endpoints = [ep for ep in all_endpoints.keys() if 'task' in ep.lower()]
    if task_endpoints:
        print("\n📝 任务相关端点:")
        for ep in task_endpoints:
            print(f"  - {ep} ({all_endpoints[ep]['file']})")
    
    # 4. Bug相关端点分析
    bug_endpoints = [ep for ep in all_endpoints.keys() if 'bug' in ep.lower()]
    if bug_endpoints:
        print("\n🐛 Bug相关端点:")
        for ep in bug_endpoints:
            print(f"  - {ep} ({all_endpoints[ep]['file']})")
    
    # 5. 项目相关端点分析
    project_endpoints = [ep for ep in all_endpoints.keys() if 'project' in ep.lower()]
    if project_endpoints:
        print("\n🏗️  项目相关端点:")
        for ep in project_endpoints:
            print(f"  - {ep} ({all_endpoints[ep]['file']})")
    
    # 6. 部门相关端点分析
    dept_endpoints = [ep for ep in all_endpoints.keys() if 'dept' in ep.lower() or 'department' in ep.lower()]
    if dept_endpoints:
        print("\n🏢 部门相关端点:")
        for ep in dept_endpoints:
            print(f"  - {ep} ({all_endpoints[ep]['file']})")
    
    # 7. 分析相关端点分析
    analysis_endpoints = [ep for ep in all_endpoints.keys() if 'analyz' in ep.lower()]
    if analysis_endpoints:
        print("\n📈 分析相关端点:")
        for ep in analysis_endpoints:
            print(f"  - {ep} ({all_endpoints[ep]['file']})")
    
    # 重复检查总结
    print("\n🎯 重复逻辑检查结果:")
    print("=" * 60)
    
    duplicates_found = []
    
    # 检查用户端点重复
    if 'zentao_get_users_by_accounts' in all_endpoints and 'zentao_get_users_by_account' in all_endpoints:
        duplicates_found.append({
            'type': '完全重复',
            'endpoints': ['zentao_get_users_by_accounts', 'zentao_get_users_by_account'],
            'reason': '两个端点功能完全相同，都是根据账号列表查询用户信息',
            'suggestion': '保留 zentao_get_users_by_accounts，移除 zentao_get_users_by_account'
        })
    
    # 检查需求端点重复
    if 'zentao_get_story_info' in all_endpoints and 'zentao_get_story_detail' in all_endpoints:
        duplicates_found.append({
            'type': '可能重复',
            'endpoints': ['zentao_get_story_info', 'zentao_get_story_detail'],
            'reason': '两个端点都是查询需求信息，需要确认业务逻辑差异',
            'suggestion': '检查服务层实现，确认是否可以合并'
        })
    
    # 检查时间范围查询重复
    time_range_endpoints = [ep for ep in all_endpoints.keys() if 'by_time' in ep.lower()]
    if len(time_range_endpoints) > 3:
        duplicates_found.append({
            'type': '模式重复',
            'endpoints': time_range_endpoints,
            'reason': '多个端点都使用时间范围查询模式，参数和逻辑相似',
            'suggestion': '可以考虑提取公共的时间范围查询装饰器'
        })
    
    if duplicates_found:
        for i, dup in enumerate(duplicates_found, 1):
            print(f"\n{i}. {dup['type']}:")
            print(f"   端点: {', '.join(dup['endpoints'])}")
            print(f"   原因: {dup['reason']}")
            print(f"   建议: {dup['suggestion']}")
    else:
        print("\n✅ 未发现明显的重复逻辑")
    
    # 代码质量分析
    print("\n📋 代码质量分析:")
    print("=" * 60)
    
    quality_issues = []
    
    # 检查错误处理重复
    error_handling_count = 0
    for file_name in os.listdir(endpoints_dir):
        if file_name.endswith('.py') and file_name != '__init__.py':
            file_path = os.path.join(endpoints_dir, file_name)
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                if 'except HTTPException:' in content and 'raise' in content:
                    error_handling_count += 1
    
    if error_handling_count > 3:
        quality_issues.append("错误处理逻辑在多个文件中重复，可以提取公共异常处理器")
    
    # 检查参数验证重复
    validation_patterns = ['verify_api_key', 'get_db', 'Depends']
    validation_count = 0
    for file_name in os.listdir(endpoints_dir):
        if file_name.endswith('.py') and file_name != '__init__.py':
            file_path = os.path.join(endpoints_dir, file_name)
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                if all(pattern in content for pattern in validation_patterns):
                    validation_count += 1
    
    if validation_count > 3:
        quality_issues.append("参数验证逻辑在多个文件中重复，可以提取公共验证装饰器")
    
    if quality_issues:
        print("\n发现的质量问题:")
        for i, issue in enumerate(quality_issues, 1):
            print(f"  {i}. {issue}")
    else:
        print("\n✅ 代码质量良好，无明显重复问题")
    
    print(f"\n📈 最终统计:")
    print(f"  总端点数: {len(all_endpoints)}")
    print(f"  重复问题: {len(duplicates_found)}")
    print(f"  质量问题: {len(quality_issues)}")
    
    return all_endpoints, duplicates_found

if __name__ == "__main__":
    try:
        analyze_endpoints()
    except Exception as e:
        print(f"❌ 分析过程中出错: {e}")
        import traceback
        traceback.print_exc()
