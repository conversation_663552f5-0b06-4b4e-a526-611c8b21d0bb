#!/usr/bin/env python3
"""
端点连通性测试脚本
测试重构后的端点是否能正确响应（不测试业务逻辑，只测试路由和基本响应）
"""

import asyncio
import aiohttp
import sys
from typing import Dict, List, Any

# 测试配置
BASE_URL = "http://localhost:8000"

# 端点连通性测试用例（不需要认证，只测试路由是否存在）
CONNECTIVITY_TESTS = [
    # 系统健康检查
    {
        "name": "health_check",
        "endpoint": "/health",
        "method": "GET",
        "expected_status": [200]
    },
    
    # 部门管理端点 - 应该返回401/403（认证失败）而不是404（路由不存在）
    {
        "name": "zentao_get_all_departments",
        "endpoint": "/api/v1/zentao/departments/zentao_get_all_departments",
        "method": "POST",
        "expected_status": [401, 403, 422, 500]  # 任何非404状态都表示路由存在
    },
    {
        "name": "zentao_get_users_by_dept",
        "endpoint": "/api/v1/zentao/departments/zentao_get_users_by_dept",
        "method": "POST",
        "expected_status": [401, 403, 422, 500]
    },
    
    # 项目管理端点
    {
        "name": "zentao_get_all_projects",
        "endpoint": "/api/v1/zentao/projects/zentao_get_all_projects",
        "method": "POST",
        "expected_status": [401, 403, 422, 500]
    },
    {
        "name": "zentao_get_tasks_by_project",
        "endpoint": "/api/v1/zentao/projects/zentao_get_tasks_by_project",
        "method": "POST",
        "expected_status": [401, 403, 422, 500]
    },
    {
        "name": "zentao_get_stories_by_project",
        "endpoint": "/api/v1/zentao/projects/zentao_get_stories_by_project",
        "method": "POST",
        "expected_status": [401, 403, 422, 500]
    },
    {
        "name": "zentao_get_bugs_by_project",
        "endpoint": "/api/v1/zentao/projects/zentao_get_bugs_by_project",
        "method": "POST",
        "expected_status": [401, 403, 422, 500]
    },
    
    # 任务管理端点
    {
        "name": "zentao_get_task_detail",
        "endpoint": "/api/v1/zentao/tasks/zentao_get_task_detail",
        "method": "POST",
        "expected_status": [401, 403, 422, 500]
    },
    {
        "name": "zentao_get_tasks_by_account",
        "endpoint": "/api/v1/zentao/tasks/zentao_get_tasks_by_account",
        "method": "POST",
        "expected_status": [401, 403, 422, 500]
    },
    {
        "name": "zentao_get_tasks_by_dept",
        "endpoint": "/api/v1/zentao/tasks/zentao_get_tasks_by_dept",
        "method": "POST",
        "expected_status": [401, 403, 422, 500]
    },
    
    # 需求管理端点
    {
        "name": "zentao_get_story_detail",
        "endpoint": "/api/v1/zentao/stories/zentao_get_story_detail",
        "method": "POST",
        "expected_status": [401, 403, 422, 500]
    },
    {
        "name": "zentao_get_story_info",
        "endpoint": "/api/v1/zentao/stories/zentao_get_story_info",
        "method": "POST",
        "expected_status": [401, 403, 422, 500]
    },
    {
        "name": "zentao_get_stories_end_info",
        "endpoint": "/api/v1/zentao/stories/zentao_get_stories_end_info",
        "method": "POST",
        "expected_status": [401, 403, 422, 500]
    },
    {
        "name": "zentao_check_story_exists",
        "endpoint": "/api/v1/zentao/stories/zentao_check_story_exists",
        "method": "POST",
        "expected_status": [401, 403, 422, 500]
    },
    {
        "name": "zentao_get_stories_by_time",
        "endpoint": "/api/v1/zentao/stories/zentao_get_stories_by_time",
        "method": "POST",
        "expected_status": [401, 403, 422, 500]
    },
    
    # Bug管理端点
    {
        "name": "zentao_get_bugs_by_time_range",
        "endpoint": "/api/v1/zentao/bugs/zentao_get_bugs_by_time_range",
        "method": "POST",
        "expected_status": [401, 403, 422, 500]
    },
    {
        "name": "zentao_get_bug_detail",
        "endpoint": "/api/v1/zentao/bugs/zentao_get_bug_detail",
        "method": "POST",
        "expected_status": [401, 403, 422, 500]
    },
    {
        "name": "zentao_get_personal_bugs",
        "endpoint": "/api/v1/zentao/bugs/zentao_get_personal_bugs",
        "method": "POST",
        "expected_status": [401, 403, 422, 500]
    },
    {
        "name": "zentao_get_bugs_by_time_and_dept",
        "endpoint": "/api/v1/zentao/bugs/zentao_get_bugs_by_time_and_dept",
        "method": "POST",
        "expected_status": [401, 403, 422, 500]
    },
    
    # 用户管理端点
    {
        "name": "zentao_get_user_info",
        "endpoint": "/api/v1/zentao/users/zentao_get_user_info",
        "method": "POST",
        "expected_status": [401, 403, 422, 500]
    },
    {
        "name": "zentao_get_users_by_accounts",
        "endpoint": "/api/v1/zentao/users/zentao_get_users_by_accounts",
        "method": "POST",
        "expected_status": [401, 403, 422, 500]
    },

    
    # 分析端点
    {
        "name": "zentao_get_project_analysis",
        "endpoint": "/api/v1/zentao/analysis/zentao_get_project_analysis",
        "method": "POST",
        "expected_status": [401, 403, 422, 500]
    },
    {
        "name": "analyze_story_workload",
        "endpoint": "/api/v1/zentao/analysis/analyze_story_workload",
        "method": "POST",
        "expected_status": [401, 403, 422, 500]
    },
    {
        "name": "project_summary_analysis",
        "endpoint": "/api/v1/zentao/analysis/project_summary_analysis",
        "method": "POST",
        "expected_status": [401, 403, 422, 500]
    }
]


async def test_connectivity(session: aiohttp.ClientSession, test_case: Dict[str, Any]) -> Dict[str, Any]:
    """测试端点连通性"""
    url = f"{BASE_URL}{test_case['endpoint']}"
    headers = {"Content-Type": "application/json"}
    
    try:
        if test_case["method"] == "GET":
            async with session.get(url, headers=headers) as response:
                status = response.status
        else:  # POST
            async with session.post(url, json={}, headers=headers) as response:
                status = response.status
        
        # 检查状态码是否在预期范围内
        is_connected = status in test_case["expected_status"]
        is_route_exists = status != 404  # 404表示路由不存在
        
        return {
            "name": test_case["name"],
            "status": status,
            "connected": is_connected,
            "route_exists": is_route_exists,
            "success": is_route_exists  # 只要路由存在就算成功
        }
    
    except Exception as e:
        return {
            "name": test_case["name"],
            "status": 0,
            "connected": False,
            "route_exists": False,
            "success": False,
            "error": str(e)
        }


async def run_connectivity_tests():
    """运行连通性测试"""
    print("🔗 开始测试端点连通性")
    print("=" * 50)
    print("说明：测试端点路由是否存在，不测试业务逻辑")
    print("预期：健康检查返回200，其他端点返回401/403/422/500（非404）")
    print("=" * 50)
    
    async with aiohttp.ClientSession() as session:
        results = []
        
        for test_case in CONNECTIVITY_TESTS:
            print(f"🔍 测试 {test_case['name']}...")
            result = await test_connectivity(session, test_case)
            results.append(result)
            
            if result["success"]:
                if result["status"] == 200:
                    print(f"✅ {result['name']} - 状态码: {result['status']} (正常)")
                else:
                    print(f"✅ {result['name']} - 状态码: {result['status']} (路由存在)")
            else:
                if result["status"] == 404:
                    print(f"❌ {result['name']} - 状态码: 404 (路由不存在)")
                else:
                    print(f"❌ {result['name']} - 状态码: {result['status']}")
                    if "error" in result:
                        print(f"   错误: {result['error']}")
        
        # 统计结果
        print("\n" + "=" * 50)
        print("📊 连通性测试结果")
        print("=" * 50)
        
        total = len(results)
        success = sum(1 for r in results if r["success"])
        route_exists = sum(1 for r in results if r["route_exists"])
        failed = total - success
        
        print(f"总端点数: {total}")
        print(f"路由存在: {route_exists}")
        print(f"连通成功: {success}")
        print(f"连通失败: {failed}")
        print(f"路由存在率: {route_exists/total*100:.1f}%")
        print(f"连通成功率: {success/total*100:.1f}%")
        
        # 显示失败的测试
        failed_tests = [r for r in results if not r["success"]]
        if failed_tests:
            print("\n❌ 连通失败的端点:")
            for test in failed_tests:
                print(f"  - {test['name']}: 状态码 {test['status']}")
                if "error" in test:
                    print(f"    错误: {test['error']}")
        else:
            print("\n🎉 所有端点路由都存在且可连通！")
        
        return success == total


if __name__ == "__main__":
    try:
        success = asyncio.run(run_connectivity_tests())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⚠️ 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 测试执行出错: {e}")
        sys.exit(1)
