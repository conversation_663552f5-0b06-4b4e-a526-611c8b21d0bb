#!/usr/bin/env python3
"""
测试重构后的端点脚本
验证所有重构后的API端点是否正常工作
"""

import sys
import os
import asyncio
import httpx
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 测试配置
BASE_URL = "http://localhost:8001"
API_KEY = "f2d226760eb2c0decca279ca4a8743ef40469f6957e40f4341c1b0093990b1c7"  # 实际的API Key

# 重构后的端点列表
REFACTORED_ENDPOINTS = {
    "departments": [
        "/api/v1/zentao/departments/zentao_get_all_departments",
        "/api/v1/zentao/departments/zentao_get_users_by_dept"
    ],
    "projects": [
        "/api/v1/zentao/projects/zentao_get_all_projects",
        "/api/v1/zentao/projects/zentao_get_tasks_by_project",
        "/api/v1/zentao/projects/zentao_get_stories_by_project",
        "/api/v1/zentao/projects/zentao_get_bugs_by_project"
    ],
    "tasks": [
        "/api/v1/zentao/tasks/zentao_get_task_detail",
        "/api/v1/zentao/tasks/zentao_get_tasks_by_account",
        "/api/v1/zentao/tasks/zentao_get_tasks_by_dept"
    ],
    "stories": [
        "/api/v1/zentao/stories/zentao_get_story_info",
        "/api/v1/zentao/stories/zentao_get_story_detail",
        "/api/v1/zentao/stories/zentao_get_stories_end_info",
        "/api/v1/zentao/stories/zentao_check_story_exists",
        "/api/v1/zentao/stories/zentao_get_stories_by_time"
    ]
}

# MCP工具端点（应该保持不变）
MCP_TOOLS_ENDPOINTS = [
    "/api/v1/mcp/tools/zentao_get_all_projects",
    "/api/v1/mcp/tools/zentao_get_all_departments",
    "/api/v1/mcp/tools/zentao_get_task_detail",
    "/api/v1/mcp/tools/zentao_get_story_detail"
]

# 已移除的端点（应该返回404）
REMOVED_ENDPOINTS = [
    "/api/v1/system/health",
    "/api/v1/mcp/resources/project_list",
    "/api/v1/mcp/resources/bug_list",
    "/api/v1/mcp/tools/zentao_get_system_info",
    "/api/v1/mcp/tools/mcp_get_health_status",
    "/api/v1/mcp/tools/mcp_get_performance_metrics"
]

async def test_endpoint(client: httpx.AsyncClient, endpoint: str, method: str = "POST", data: dict = None):
    """测试单个端点"""
    headers = {
        "Authorization": f"Bearer {API_KEY}",
        "Content-Type": "application/json"
    }
    
    try:
        if method == "POST":
            response = await client.post(f"{BASE_URL}{endpoint}", json=data or {}, headers=headers)
        else:
            response = await client.get(f"{BASE_URL}{endpoint}", headers=headers)
        
        return {
            "endpoint": endpoint,
            "status_code": response.status_code,
            "success": response.status_code in [200, 401, 422],  # 401=未授权, 422=参数错误都是正常的
            "error": None
        }
    except Exception as e:
        return {
            "endpoint": endpoint,
            "status_code": None,
            "success": False,
            "error": str(e)
        }

async def test_removed_endpoint(client: httpx.AsyncClient, endpoint: str):
    """测试已移除的端点（应该返回404）"""
    headers = {
        "Authorization": f"Bearer {API_KEY}",
        "Content-Type": "application/json"
    }
    
    try:
        response = await client.post(f"{BASE_URL}{endpoint}", json={}, headers=headers)
        return {
            "endpoint": endpoint,
            "status_code": response.status_code,
            "success": response.status_code == 404,  # 应该返回404
            "error": None
        }
    except Exception as e:
        return {
            "endpoint": endpoint,
            "status_code": None,
            "success": False,
            "error": str(e)
        }

async def main():
    """主测试函数"""
    print("🧪 开始测试重构后的端点...")
    print("=" * 60)
    
    async with httpx.AsyncClient(timeout=30.0) as client:
        all_results = []
        
        # 测试重构后的端点
        print("\n📋 测试重构后的端点:")
        print("-" * 40)
        
        for category, endpoints in REFACTORED_ENDPOINTS.items():
            print(f"\n🔧 测试 {category} 端点:")
            for endpoint in endpoints:
                # 根据端点类型提供测试数据
                test_data = {}
                if "dept" in endpoint:
                    test_data = {"dept_id": 1}
                elif "project" in endpoint:
                    test_data = {"project_id": 1}
                elif "task" in endpoint:
                    if "account" in endpoint:
                        test_data = {"account": "test", "start_date": "2024-01-01 00:00:00", "end_date": "2024-12-31 23:59:59"}
                    elif "dept" in endpoint:
                        test_data = {"dept_id": 1, "start_date": "2024-01-01 00:00:00", "end_date": "2024-12-31 23:59:59"}
                    else:
                        test_data = {"task_id": 1}
                elif "story" in endpoint:
                    if "info" in endpoint or "exists" in endpoint or "end" in endpoint:
                        test_data = {"story_ids": ["1", "2"]}
                    elif "time" in endpoint:
                        test_data = {"status": "active", "start_date": "2024-01-01 00:00:00", "end_date": "2024-12-31 23:59:59"}
                    else:
                        test_data = {"story_id": 1}
                
                result = await test_endpoint(client, endpoint, "POST", test_data)
                all_results.append(result)
                
                status_icon = "✅" if result["success"] else "❌"
                print(f"  {status_icon} {endpoint} - {result['status_code']}")
                if result["error"]:
                    print(f"     错误: {result['error']}")
        
        # 测试MCP工具端点（应该保持不变）
        print(f"\n🔧 测试MCP工具端点:")
        print("-" * 40)
        
        for endpoint in MCP_TOOLS_ENDPOINTS:
            test_data = {}
            if "project" in endpoint:
                test_data = {"project_id": 1}
            elif "dept" in endpoint:
                test_data = {"dept_id": 1}
            elif "task" in endpoint:
                test_data = {"task_id": 1}
            elif "story" in endpoint:
                test_data = {"story_id": 1}
            
            result = await test_endpoint(client, endpoint, "POST", test_data)
            all_results.append(result)
            
            status_icon = "✅" if result["success"] else "❌"
            print(f"  {status_icon} {endpoint} - {result['status_code']}")
            if result["error"]:
                print(f"     错误: {result['error']}")
        
        # 测试已移除的端点
        print(f"\n🗑️  测试已移除的端点 (应该返回404):")
        print("-" * 40)
        
        for endpoint in REMOVED_ENDPOINTS:
            result = await test_removed_endpoint(client, endpoint)
            all_results.append(result)
            
            status_icon = "✅" if result["success"] else "❌"
            expected = "404 (已移除)" if result["success"] else f"{result['status_code']} (应该是404)"
            print(f"  {status_icon} {endpoint} - {expected}")
            if result["error"]:
                print(f"     错误: {result['error']}")
    
    # 统计结果
    print("\n" + "=" * 60)
    print("📊 测试结果统计:")
    
    total_tests = len(all_results)
    successful_tests = sum(1 for r in all_results if r["success"])
    failed_tests = total_tests - successful_tests
    
    print(f"总测试数: {total_tests}")
    print(f"成功: {successful_tests}")
    print(f"失败: {failed_tests}")
    print(f"成功率: {successful_tests/total_tests*100:.1f}%")
    
    if failed_tests > 0:
        print(f"\n❌ 失败的测试:")
        for result in all_results:
            if not result["success"]:
                print(f"  - {result['endpoint']}: {result['status_code']} {result.get('error', '')}")
    
    print("\n🎉 重构端点测试完成!")
    return failed_tests == 0

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
