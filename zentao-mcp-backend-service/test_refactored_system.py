#!/usr/bin/env python3
"""
重构后系统测试脚本
测试所有重构后的zentao端点是否正常工作
"""

import asyncio
import aiohttp
import json
import sys
from typing import Dict, List, Any
import os

# 测试配置
BASE_URL = "http://localhost:8000"
API_KEY = os.getenv("ZENTAO_API_KEY", "test-api-key-12345")

# 重构后的端点测试用例
TEST_CASES = [
    # 系统健康检查
    {
        "name": "health_check",
        "endpoint": "/health",
        "method": "GET",
        "data": None,
        "category": "system",
        "auth_required": False
    },
    
    # 部门管理端点
    {
        "name": "zentao_get_all_departments",
        "endpoint": "/api/v1/zentao/departments/zentao_get_all_departments",
        "method": "POST",
        "data": {},
        "category": "departments",
        "auth_required": True
    },
    {
        "name": "zentao_get_users_by_dept",
        "endpoint": "/api/v1/zentao/departments/zentao_get_users_by_dept",
        "method": "POST",
        "data": {"dept_id": 1},
        "category": "departments",
        "auth_required": True
    },
    
    # 项目管理端点
    {
        "name": "zentao_get_all_projects",
        "endpoint": "/api/v1/zentao/projects/zentao_get_all_projects",
        "method": "POST",
        "data": {},
        "category": "projects",
        "auth_required": True
    },
    {
        "name": "zentao_get_tasks_by_project",
        "endpoint": "/api/v1/zentao/projects/zentao_get_tasks_by_project",
        "method": "POST",
        "data": {"project_id": 1},
        "category": "projects",
        "auth_required": True
    },
    {
        "name": "zentao_get_stories_by_project",
        "endpoint": "/api/v1/zentao/projects/zentao_get_stories_by_project",
        "method": "POST",
        "data": {"project_id": 1},
        "category": "projects",
        "auth_required": True
    },
    {
        "name": "zentao_get_bugs_by_project",
        "endpoint": "/api/v1/zentao/projects/zentao_get_bugs_by_project",
        "method": "POST",
        "data": {"project_id": 1},
        "category": "projects",
        "auth_required": True
    },
    
    # 任务管理端点
    {
        "name": "zentao_get_task_detail",
        "endpoint": "/api/v1/zentao/tasks/zentao_get_task_detail",
        "method": "POST",
        "data": {"task_id": 1},
        "category": "tasks",
        "auth_required": True
    },
    {
        "name": "zentao_get_tasks_by_account",
        "endpoint": "/api/v1/zentao/tasks/zentao_get_tasks_by_account",
        "method": "POST",
        "data": {
            "account": "admin",
            "start_date": "2024-01-01 00:00:00",
            "end_date": "2024-12-31 23:59:59",
            "is_doing": False
        },
        "category": "tasks",
        "auth_required": True
    },
    {
        "name": "zentao_get_tasks_by_dept",
        "endpoint": "/api/v1/zentao/tasks/zentao_get_tasks_by_dept",
        "method": "POST",
        "data": {
            "dept_id": 1,
            "start_date": "2024-01-01 00:00:00",
            "end_date": "2024-12-31 23:59:59",
            "is_doing": False
        },
        "category": "tasks",
        "auth_required": True
    },
    
    # 需求管理端点
    {
        "name": "zentao_get_story_detail",
        "endpoint": "/api/v1/zentao/stories/zentao_get_story_detail",
        "method": "POST",
        "data": {"story_id": 1},
        "category": "stories",
        "auth_required": True
    },
    {
        "name": "zentao_get_story_info",
        "endpoint": "/api/v1/zentao/stories/zentao_get_story_info",
        "method": "POST",
        "data": {"story_ids": ["1", "2"]},
        "category": "stories",
        "auth_required": True
    },
    {
        "name": "zentao_get_stories_end_info",
        "endpoint": "/api/v1/zentao/stories/zentao_get_stories_end_info",
        "method": "POST",
        "data": {"story_ids": ["1", "2"]},
        "category": "stories",
        "auth_required": True
    },
    {
        "name": "zentao_check_story_exists",
        "endpoint": "/api/v1/zentao/stories/zentao_check_story_exists",
        "method": "POST",
        "data": {"story_ids": ["1", "2"]},
        "category": "stories",
        "auth_required": True
    },
    {
        "name": "zentao_get_stories_by_time",
        "endpoint": "/api/v1/zentao/stories/zentao_get_stories_by_time",
        "method": "POST",
        "data": {
            "status": "active",
            "start_date": "2024-01-01 00:00:00",
            "end_date": "2024-12-31 23:59:59"
        },
        "category": "stories",
        "auth_required": True
    },
    
    # Bug管理端点
    {
        "name": "zentao_get_bugs_by_time_range",
        "endpoint": "/api/v1/zentao/bugs/zentao_get_bugs_by_time_range",
        "method": "POST",
        "data": {
            "start_date": "2024-01-01 00:00:00",
            "end_date": "2024-12-31 23:59:59"
        },
        "category": "bugs",
        "auth_required": True
    },
    {
        "name": "zentao_get_bug_detail",
        "endpoint": "/api/v1/zentao/bugs/zentao_get_bug_detail",
        "method": "POST",
        "data": {"bug_id": 1},
        "category": "bugs",
        "auth_required": True
    },
    {
        "name": "zentao_get_personal_bugs",
        "endpoint": "/api/v1/zentao/bugs/zentao_get_personal_bugs",
        "method": "POST",
        "data": {
            "account": "admin",
            "status": "active",
            "start_date": "2024-01-01 00:00:00",
            "end_date": "2024-12-31 23:59:59"
        },
        "category": "bugs",
        "auth_required": True
    },
    {
        "name": "zentao_get_bugs_by_time_and_dept",
        "endpoint": "/api/v1/zentao/bugs/zentao_get_bugs_by_time_and_dept",
        "method": "POST",
        "data": {
            "start_date": "2024-01-01 00:00:00",
            "end_date": "2024-12-31 23:59:59",
            "dept_id": 1
        },
        "category": "bugs",
        "auth_required": True
    },
    
    # 用户管理端点
    {
        "name": "zentao_get_user_info",
        "endpoint": "/api/v1/zentao/users/zentao_get_user_info",
        "method": "POST",
        "data": {"user_account": "admin"},
        "category": "users",
        "auth_required": True
    },
    {
        "name": "zentao_get_users_by_accounts",
        "endpoint": "/api/v1/zentao/users/zentao_get_users_by_accounts",
        "method": "POST",
        "data": {"accounts": ["admin", "test"]},
        "category": "users",
        "auth_required": True
    },

    
    # 分析端点
    {
        "name": "zentao_get_project_analysis",
        "endpoint": "/api/v1/zentao/analysis/zentao_get_project_analysis",
        "method": "POST",
        "data": {
            "project_id": 1,
            "start_date": "2024-01-01 00:00:00",
            "end_date": "2024-12-31 23:59:59"
        },
        "category": "analysis",
        "auth_required": True
    },
    {
        "name": "analyze_story_workload",
        "endpoint": "/api/v1/zentao/analysis/analyze_story_workload",
        "method": "POST",
        "data": {"project_id": 1, "include_completed": True},
        "category": "analysis",
        "auth_required": True
    },
    {
        "name": "project_summary_analysis",
        "endpoint": "/api/v1/zentao/analysis/project_summary_analysis",
        "method": "POST",
        "data": {"project_id": 1},
        "category": "analysis",
        "auth_required": True
    }
]


async def test_endpoint(session: aiohttp.ClientSession, test_case: Dict[str, Any]) -> Dict[str, Any]:
    """测试单个端点"""
    url = f"{BASE_URL}{test_case['endpoint']}"
    headers = {"Content-Type": "application/json"}
    
    # 添加认证头
    if test_case.get("auth_required", False):
        headers["Authorization"] = f"Bearer {API_KEY}"
    
    try:
        if test_case["method"] == "GET":
            async with session.get(url, headers=headers) as response:
                status = response.status
                try:
                    data = await response.json()
                except:
                    data = await response.text()
        else:  # POST
            async with session.post(url, json=test_case["data"], headers=headers) as response:
                status = response.status
                try:
                    data = await response.json()
                except:
                    data = await response.text()
        
        return {
            "name": test_case["name"],
            "category": test_case["category"],
            "status": status,
            "success": status < 400,
            "response": data
        }
    
    except Exception as e:
        return {
            "name": test_case["name"],
            "category": test_case["category"],
            "status": 0,
            "success": False,
            "error": str(e)
        }


async def run_tests():
    """运行所有测试"""
    print("🚀 开始测试重构后的系统")
    print("=" * 50)
    
    async with aiohttp.ClientSession() as session:
        results = []
        
        for test_case in TEST_CASES:
            print(f"🧪 测试 {test_case['name']} ({test_case['category']})...")
            result = await test_endpoint(session, test_case)
            results.append(result)
            
            if result["success"]:
                print(f"✅ {result['name']} - 状态码: {result['status']}")
            else:
                print(f"❌ {result['name']} - 状态码: {result['status']}")
                if "error" in result:
                    print(f"   错误: {result['error']}")
        
        # 统计结果
        print("\n" + "=" * 50)
        print("📊 测试结果统计")
        print("=" * 50)
        
        total = len(results)
        success = sum(1 for r in results if r["success"])
        failed = total - success
        
        print(f"总测试数: {total}")
        print(f"成功: {success}")
        print(f"失败: {failed}")
        print(f"成功率: {success/total*100:.1f}%")
        
        # 按类别统计
        categories = {}
        for result in results:
            cat = result["category"]
            if cat not in categories:
                categories[cat] = {"total": 0, "success": 0}
            categories[cat]["total"] += 1
            if result["success"]:
                categories[cat]["success"] += 1
        
        print("\n📈 分类统计:")
        for cat, stats in categories.items():
            rate = stats["success"] / stats["total"] * 100
            print(f"  {cat}: {stats['success']}/{stats['total']} ({rate:.1f}%)")
        
        # 显示失败的测试
        failed_tests = [r for r in results if not r["success"]]
        if failed_tests:
            print("\n❌ 失败的测试:")
            for test in failed_tests:
                print(f"  - {test['name']}: 状态码 {test['status']}")
                if "error" in test:
                    print(f"    错误: {test['error']}")
        
        return success == total


if __name__ == "__main__":
    try:
        success = asyncio.run(run_tests())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⚠️ 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 测试执行出错: {e}")
        sys.exit(1)
