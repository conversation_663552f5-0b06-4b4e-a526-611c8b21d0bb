#!/usr/bin/env python3
"""
客户端-服务端连通性测试
测试客户端工具端点映射是否与服务端匹配
"""

import asyncio
import sys
from pathlib import Path

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent))

from zentao_mcp_client.config import ClientConfig
from zentao_mcp_client.proxy import ZentaoMCPProxy
import httpx

async def test_client_server_connectivity():
    """测试客户端与服务端的连通性"""
    print("🔗 客户端-服务端连通性测试")
    print("=" * 60)
    
    # 初始化配置
    config = ClientConfig()
    
    # 检查配置
    if not config.is_configured():
        print("❌ 客户端未配置，请先配置backend_url和api_key")
        return False
    
    backend_url = config.get_backend_url()
    api_key = config.get_api_key()
    
    print(f"后端服务: {backend_url}")
    print(f"API Key: {api_key[:10]}..." if api_key else "未配置")
    print()
    
    # 创建代理实例
    proxy = ZentaoMCPProxy(config)
    
    try:
        # 测试健康检查
        print("🏥 测试服务健康状态...")
        async with httpx.AsyncClient() as client:
            try:
                response = await client.get(f"{backend_url}/health")
                if response.status_code == 200:
                    print("✅ 服务健康检查通过")
                else:
                    print(f"❌ 服务健康检查失败: {response.status_code}")
                    return False
            except Exception as e:
                print(f"❌ 无法连接到服务: {e}")
                return False
        
        # 测试端点映射
        print("\n🔍 测试端点映射...")
        print("-" * 40)
        
        success_count = 0
        total_count = len(proxy.tool_endpoints)
        
        async with httpx.AsyncClient() as client:
            for tool_name, endpoint in proxy.tool_endpoints.items():
                try:
                    # 发送测试请求（不需要认证，只测试路由是否存在）
                    response = await client.post(f"{backend_url}{endpoint}", json={})
                    
                    # 403表示路由存在但需要认证，404表示路由不存在
                    if response.status_code in [403, 422, 500]:
                        print(f"✅ {tool_name}")
                        success_count += 1
                    elif response.status_code == 404:
                        print(f"❌ {tool_name} - 端点不存在")
                    else:
                        print(f"⚠️  {tool_name} - 状态码: {response.status_code}")
                        success_count += 1  # 非404都算成功
                        
                except Exception as e:
                    print(f"❌ {tool_name} - 连接错误: {e}")
        
        # 统计结果
        print(f"\n📊 测试结果:")
        print(f"总端点数: {total_count}")
        print(f"连通成功: {success_count}")
        print(f"连通失败: {total_count - success_count}")
        print(f"成功率: {success_count/total_count*100:.1f}%")
        
        # 测试实际工具调用（需要认证）
        print(f"\n🧪 测试实际工具调用...")
        print("-" * 40)
        
        test_tools = [
            ("zentao_get_all_departments", {}),
            ("zentao_get_all_projects", {}),
        ]
        
        tool_success = 0
        for tool_name, args in test_tools:
            try:
                result = await proxy._forward_request(tool_name, args)
                if result:
                    print(f"✅ {tool_name} - 调用成功")
                    tool_success += 1
                else:
                    print(f"❌ {tool_name} - 返回空结果")
            except Exception as e:
                print(f"❌ {tool_name} - 调用失败: {e}")
        
        print(f"\n工具调用成功率: {tool_success}/{len(test_tools)} ({tool_success/len(test_tools)*100:.1f}%)")
        
        # 最终结果
        overall_success = (success_count == total_count) and (tool_success > 0)
        
        if overall_success:
            print("\n🎉 客户端-服务端连通性测试通过！")
        else:
            print("\n⚠️  客户端-服务端连通性测试部分失败")
        
        return overall_success
        
    finally:
        await proxy.close()

async def test_endpoint_mapping_consistency():
    """测试端点映射一致性"""
    print("\n🔍 端点映射一致性检查")
    print("=" * 60)
    
    config = ClientConfig()
    proxy = ZentaoMCPProxy(config)
    
    try:
        # 检查端点路径格式
        format_issues = []
        for tool_name, endpoint in proxy.tool_endpoints.items():
            if not endpoint.startswith("/api/v1/zentao/"):
                format_issues.append(f"{tool_name}: {endpoint}")
        
        if format_issues:
            print("❌ 端点路径格式问题:")
            for issue in format_issues:
                print(f"  - {issue}")
        else:
            print("✅ 所有端点路径格式正确")
        
        # 检查重复端点
        endpoints = list(proxy.tool_endpoints.values())
        duplicates = []
        for endpoint in set(endpoints):
            if endpoints.count(endpoint) > 1:
                tools = [tool for tool, ep in proxy.tool_endpoints.items() if ep == endpoint]
                duplicates.append((endpoint, tools))
        
        if duplicates:
            print("\n❌ 发现重复端点:")
            for endpoint, tools in duplicates:
                print(f"  - {endpoint}: {', '.join(tools)}")
        else:
            print("✅ 无重复端点")
        
        # 检查工具数量
        total_tools = len(proxy.tool_endpoints)
        print(f"\n📊 工具统计:")
        print(f"总工具数: {total_tools}")
        
        # 按模块分组
        modules = {}
        for tool_name, endpoint in proxy.tool_endpoints.items():
            module = endpoint.split('/')[4] if len(endpoint.split('/')) > 4 else 'unknown'
            if module not in modules:
                modules[module] = 0
            modules[module] += 1
        
        print("模块分布:")
        for module, count in sorted(modules.items()):
            print(f"  {module}: {count} 个工具")
        
        return len(format_issues) == 0 and len(duplicates) == 0
        
    finally:
        await proxy.close()

async def main():
    """主函数"""
    try:
        print("🚀 开始客户端测试")
        print("=" * 60)
        
        # 测试端点映射一致性
        mapping_ok = await test_endpoint_mapping_consistency()
        
        # 测试连通性
        connectivity_ok = await test_client_server_connectivity()
        
        # 总结
        print("\n" + "=" * 60)
        print("📋 测试总结")
        print("=" * 60)
        
        if mapping_ok:
            print("✅ 端点映射一致性检查通过")
        else:
            print("❌ 端点映射一致性检查失败")
        
        if connectivity_ok:
            print("✅ 客户端-服务端连通性测试通过")
        else:
            print("❌ 客户端-服务端连通性测试失败")
        
        overall_success = mapping_ok and connectivity_ok
        
        if overall_success:
            print("\n🎉 所有测试通过！客户端可以正常使用。")
        else:
            print("\n⚠️  部分测试失败，请检查配置和服务状态。")
        
        return overall_success
        
    except Exception as e:
        print(f"\n💥 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⚠️ 测试被用户中断")
        sys.exit(1)
